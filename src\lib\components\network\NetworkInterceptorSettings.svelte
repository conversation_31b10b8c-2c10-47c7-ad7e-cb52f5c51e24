<script lang="ts">
	import { onMount } from 'svelte';
	import {
		networkConfig,
		networkInterceptorEnabled,
		isTauriEnvironment,
		networkStats,
		networkSuccessRate,
		networkInterceptorActions,
		initializeNetworkStores
	} from '$lib/stores/network';
	import type { NetworkInterceptorConfig } from '$lib/types/tauri';

	let localConfig: NetworkInterceptorConfig;
	let showAdvanced = false;
	let newPattern = '';
	let newExcludePattern = '';

	// 订阅配置变化
	$: localConfig = $networkConfig;

	onMount(() => {
		initializeNetworkStores();
	});

	async function toggleInterceptor() {
		try {
			if ($networkInterceptorEnabled) {
				await networkInterceptorActions.disable();
			} else {
				await networkInterceptorActions.enable();
			}
		} catch (error) {
			console.error('切换网络拦截器状态失败:', error);
		}
	}

	async function updateConfig() {
		try {
			await networkInterceptorActions.updateConfig(localConfig);
		} catch (error) {
			console.error('更新配置失败:', error);
		}
	}

	function addInterceptPattern() {
		if (newPattern.trim()) {
			localConfig.interceptPatterns = [...localConfig.interceptPatterns, newPattern.trim()];
			newPattern = '';
			updateConfig();
		}
	}

	function removeInterceptPattern(index: number) {
		localConfig.interceptPatterns = localConfig.interceptPatterns.filter((_, i) => i !== index);
		updateConfig();
	}

	function addExcludePattern() {
		if (newExcludePattern.trim()) {
			localConfig.excludePatterns = [...localConfig.excludePatterns, newExcludePattern.trim()];
			newExcludePattern = '';
			updateConfig();
		}
	}

	function removeExcludePattern(index: number) {
		localConfig.excludePatterns = localConfig.excludePatterns.filter((_, i) => i !== index);
		updateConfig();
	}

	function resetStats() {
		networkInterceptorActions.resetStats();
	}
</script>

<div class="network-interceptor-settings p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
	<h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">网络拦截器设置</h3>

	<!-- 状态信息 -->
	<div class="mb-6 p-3 bg-gray-50 dark:bg-gray-700 rounded">
		<div class="grid grid-cols-2 gap-4 text-sm">
			<div>
				<span class="text-gray-600 dark:text-gray-300">Tauri 环境:</span>
				<span class="ml-2 {$isTauriEnvironment ? 'text-green-600' : 'text-red-600'}">
					{$isTauriEnvironment ? '已检测' : '未检测'}
				</span>
			</div>
			<div>
				<span class="text-gray-600 dark:text-gray-300">拦截器状态:</span>
				<span class="ml-2 {$networkInterceptorEnabled ? 'text-green-600' : 'text-red-600'}">
					{$networkInterceptorEnabled ? '已启用' : '已禁用'}
				</span>
			</div>
		</div>
	</div>

	<!-- 基本设置 -->
	<div class="mb-6">
		<div class="flex items-center justify-between mb-4">
			<label class="flex items-center">
				<input
					type="checkbox"
					bind:checked={$networkInterceptorEnabled}
					on:change={toggleInterceptor}
					class="mr-2"
				/>
				<span class="text-gray-700 dark:text-gray-300">启用网络拦截器</span>
			</label>
		</div>

		<div class="grid grid-cols-2 gap-4">
			<div>
				<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
					Tauri 插件名称
				</label>
				<input
					type="text"
					bind:value={localConfig.tauriPluginName}
					on:blur={updateConfig}
					class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
				/>
			</div>

			<div>
				<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
					超时时间 (毫秒)
				</label>
				<input
					type="number"
					bind:value={localConfig.timeout}
					on:blur={updateConfig}
					min="1000"
					max="300000"
					class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
				/>
			</div>
		</div>

		<div class="mt-4">
			<label class="flex items-center">
				<input
					type="checkbox"
					bind:checked={localConfig.fallbackToFetch}
					on:change={updateConfig}
					class="mr-2"
				/>
				<span class="text-gray-700 dark:text-gray-300">Tauri 失败时回退到原生 fetch</span>
			</label>
		</div>

		<div class="mt-2">
			<label class="flex items-center">
				<input
					type="checkbox"
					bind:checked={localConfig.debug}
					on:change={updateConfig}
					class="mr-2"
				/>
				<span class="text-gray-700 dark:text-gray-300">启用调试模式</span>
			</label>
		</div>
	</div>

	<!-- 高级设置 -->
	<div class="mb-6">
		<button
			on:click={() => (showAdvanced = !showAdvanced)}
			class="flex items-center text-blue-600 dark:text-blue-400 hover:underline"
		>
			<span>{showAdvanced ? '隐藏' : '显示'}高级设置</span>
			<svg
				class="ml-1 w-4 h-4 transform {showAdvanced ? 'rotate-180' : ''}"
				fill="none"
				stroke="currentColor"
				viewBox="0 0 24 24"
			>
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
			</svg>
		</button>

		{#if showAdvanced}
			<div class="mt-4 space-y-4">
				<!-- 拦截模式 -->
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
						拦截模式 (支持通配符 *)
					</label>
					<div class="space-y-2">
						{#each localConfig.interceptPatterns as pattern, index}
							<div class="flex items-center">
								<input
									type="text"
									value={pattern}
									readonly
									class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-600 text-gray-900 dark:text-white"
								/>
								<button
									on:click={() => removeInterceptPattern(index)}
									class="ml-2 px-2 py-1 text-red-600 hover:bg-red-50 dark:hover:bg-red-900 rounded"
								>
									删除
								</button>
							</div>
						{/each}
						<div class="flex items-center">
							<input
								type="text"
								bind:value={newPattern}
								placeholder="添加新的拦截模式..."
								class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
								on:keydown={(e) => e.key === 'Enter' && addInterceptPattern()}
							/>
							<button
								on:click={addInterceptPattern}
								class="ml-2 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
							>
								添加
							</button>
						</div>
					</div>
				</div>

				<!-- 排除模式 -->
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
						排除模式 (支持通配符 *)
					</label>
					<div class="space-y-2">
						{#each localConfig.excludePatterns as pattern, index}
							<div class="flex items-center">
								<input
									type="text"
									value={pattern}
									readonly
									class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-600 text-gray-900 dark:text-white"
								/>
								<button
									on:click={() => removeExcludePattern(index)}
									class="ml-2 px-2 py-1 text-red-600 hover:bg-red-50 dark:hover:bg-red-900 rounded"
								>
									删除
								</button>
							</div>
						{/each}
						<div class="flex items-center">
							<input
								type="text"
								bind:value={newExcludePattern}
								placeholder="添加新的排除模式..."
								class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
								on:keydown={(e) => e.key === 'Enter' && addExcludePattern()}
							/>
							<button
								on:click={addExcludePattern}
								class="ml-2 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
							>
								添加
							</button>
						</div>
					</div>
				</div>
			</div>
		{/if}
	</div>

	<!-- 统计信息 -->
	<div class="mb-6 p-3 bg-gray-50 dark:bg-gray-700 rounded">
		<div class="flex items-center justify-between mb-2">
			<h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">网络请求统计</h4>
			<button
				on:click={resetStats}
				class="text-xs text-blue-600 dark:text-blue-400 hover:underline"
			>
				重置
			</button>
		</div>
		<div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
			<div>
				<div class="text-gray-600 dark:text-gray-400">总请求数</div>
				<div class="font-semibold text-gray-900 dark:text-white">{$networkStats.totalRequests}</div>
			</div>
			<div>
				<div class="text-gray-600 dark:text-gray-400">Tauri 请求</div>
				<div class="font-semibold text-blue-600">{$networkStats.tauriRequests}</div>
			</div>
			<div>
				<div class="text-gray-600 dark:text-gray-400">Fetch 请求</div>
				<div class="font-semibold text-green-600">{$networkStats.fetchRequests}</div>
			</div>
			<div>
				<div class="text-gray-600 dark:text-gray-400">成功率</div>
				<div class="font-semibold text-gray-900 dark:text-white">{$networkSuccessRate}%</div>
			</div>
		</div>
		{#if $networkStats.averageResponseTime > 0}
			<div class="mt-2 text-sm">
				<span class="text-gray-600 dark:text-gray-400">平均响应时间:</span>
				<span class="ml-1 font-semibold text-gray-900 dark:text-white">
					{$networkStats.averageResponseTime.toFixed(0)}ms
				</span>
			</div>
		{/if}
	</div>
</div>

<style>
	.network-interceptor-settings {
		max-width: 800px;
	}
</style>
