<script lang="ts">
	import { onMount } from 'svelte';
	import {
		networkInterceptorEnabled,
		isTauriEnvironment,
		networkStats,
		networkEvents,
		networkInterceptorActions
	} from '$lib/stores/network';
	import type { InterceptorEvent } from '$lib/types/tauri';

	let testResults: Array<{
		id: string;
		url: string;
		method: string;
		status: 'pending' | 'success' | 'error';
		response?: any;
		error?: string;
		duration?: number;
		usedTauri?: boolean;
	}> = [];

	let isRunningTests = false;
	let recentEvents: InterceptorEvent[] = [];

	// 订阅网络事件
	$: recentEvents = $networkEvents.slice(-10).reverse();

	onMount(() => {
		// 监听网络事件变化
		const unsubscribe = networkEvents.subscribe(events => {
			recentEvents = events.slice(-10).reverse();
		});

		return unsubscribe;
	});

	async function runBasicTest() {
		const testId = `test_${Date.now()}`;
		const testUrl = 'https://jsonplaceholder.typicode.com/posts/1';
		
		const testCase = {
			id: testId,
			url: testUrl,
			method: 'GET',
			status: 'pending' as const
		};

		testResults = [...testResults, testCase];

		try {
			const startTime = Date.now();
			const response = await fetch(testUrl);
			const duration = Date.now() - startTime;
			const data = await response.json();

			// 更新测试结果
			testResults = testResults.map(test => 
				test.id === testId 
					? {
						...test,
						status: 'success' as const,
						response: data,
						duration,
						usedTauri: $isTauriEnvironment && $networkInterceptorEnabled
					}
					: test
			);
		} catch (error) {
			testResults = testResults.map(test => 
				test.id === testId 
					? {
						...test,
						status: 'error' as const,
						error: error instanceof Error ? error.message : String(error)
					}
					: test
			);
		}
	}

	async function runPostTest() {
		const testId = `test_${Date.now()}`;
		const testUrl = 'https://jsonplaceholder.typicode.com/posts';
		
		const testCase = {
			id: testId,
			url: testUrl,
			method: 'POST',
			status: 'pending' as const
		};

		testResults = [...testResults, testCase];

		try {
			const startTime = Date.now();
			const response = await fetch(testUrl, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					title: 'Test Post',
					body: 'This is a test post from network interceptor',
					userId: 1
				})
			});
			const duration = Date.now() - startTime;
			const data = await response.json();

			testResults = testResults.map(test => 
				test.id === testId 
					? {
						...test,
						status: 'success' as const,
						response: data,
						duration,
						usedTauri: $isTauriEnvironment && $networkInterceptorEnabled
					}
					: test
			);
		} catch (error) {
			testResults = testResults.map(test => 
				test.id === testId 
					? {
						...test,
						status: 'error' as const,
						error: error instanceof Error ? error.message : String(error)
					}
					: test
			);
		}
	}

	async function runMultipleTests() {
		isRunningTests = true;
		
		const urls = [
			'https://jsonplaceholder.typicode.com/posts/1',
			'https://jsonplaceholder.typicode.com/posts/2',
			'https://jsonplaceholder.typicode.com/users/1',
			'https://jsonplaceholder.typicode.com/albums/1'
		];

		for (const url of urls) {
			const testId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
			
			const testCase = {
				id: testId,
				url,
				method: 'GET',
				status: 'pending' as const
			};

			testResults = [...testResults, testCase];

			try {
				const startTime = Date.now();
				const response = await fetch(url);
				const duration = Date.now() - startTime;
				const data = await response.json();

				testResults = testResults.map(test => 
					test.id === testId 
						? {
							...test,
							status: 'success' as const,
							response: data,
							duration,
							usedTauri: $isTauriEnvironment && $networkInterceptorEnabled
						}
						: test
				);
			} catch (error) {
				testResults = testResults.map(test => 
					test.id === testId 
						? {
							...test,
							status: 'error' as const,
							error: error instanceof Error ? error.message : String(error)
						}
						: test
				);
			}

			// 添加小延迟以便观察
			await new Promise(resolve => setTimeout(resolve, 500));
		}

		isRunningTests = false;
	}

	function clearResults() {
		testResults = [];
		networkInterceptorActions.clearEvents();
	}

	function refreshStats() {
		networkInterceptorActions.refreshStats();
	}
</script>

<div class="network-interceptor-test p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
	<h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">网络拦截器测试</h3>

	<!-- 状态显示 -->
	<div class="mb-6 p-3 bg-gray-50 dark:bg-gray-700 rounded">
		<div class="grid grid-cols-2 gap-4 text-sm">
			<div>
				<span class="text-gray-600 dark:text-gray-300">Tauri 环境:</span>
				<span class="ml-2 {$isTauriEnvironment ? 'text-green-600' : 'text-red-600'}">
					{$isTauriEnvironment ? '已检测' : '未检测'}
				</span>
			</div>
			<div>
				<span class="text-gray-600 dark:text-gray-300">拦截器状态:</span>
				<span class="ml-2 {$networkInterceptorEnabled ? 'text-green-600' : 'text-red-600'}">
					{$networkInterceptorEnabled ? '已启用' : '已禁用'}
				</span>
			</div>
		</div>
	</div>

	<!-- 测试按钮 -->
	<div class="mb-6 flex flex-wrap gap-2">
		<button
			on:click={runBasicTest}
			disabled={isRunningTests}
			class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
		>
			基本 GET 测试
		</button>
		
		<button
			on:click={runPostTest}
			disabled={isRunningTests}
			class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
		>
			POST 测试
		</button>
		
		<button
			on:click={runMultipleTests}
			disabled={isRunningTests}
			class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
		>
			{isRunningTests ? '运行中...' : '批量测试'}
		</button>
		
		<button
			on:click={clearResults}
			class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
		>
			清除结果
		</button>
		
		<button
			on:click={refreshStats}
			class="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
		>
			刷新统计
		</button>
	</div>

	<!-- 统计信息 -->
	<div class="mb-6 p-3 bg-gray-50 dark:bg-gray-700 rounded">
		<h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">实时统计</h4>
		<div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
			<div>
				<div class="text-gray-600 dark:text-gray-400">总请求</div>
				<div class="font-semibold text-gray-900 dark:text-white">{$networkStats.totalRequests}</div>
			</div>
			<div>
				<div class="text-gray-600 dark:text-gray-400">Tauri 请求</div>
				<div class="font-semibold text-blue-600">{$networkStats.tauriRequests}</div>
			</div>
			<div>
				<div class="text-gray-600 dark:text-gray-400">Fetch 请求</div>
				<div class="font-semibold text-green-600">{$networkStats.fetchRequests}</div>
			</div>
			<div>
				<div class="text-gray-600 dark:text-gray-400">错误数</div>
				<div class="font-semibold text-red-600">{$networkStats.errors}</div>
			</div>
		</div>
	</div>

	<!-- 测试结果 -->
	{#if testResults.length > 0}
		<div class="mb-6">
			<h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">测试结果</h4>
			<div class="space-y-2 max-h-64 overflow-y-auto">
				{#each testResults.slice().reverse() as test}
					<div class="p-3 border border-gray-200 dark:border-gray-600 rounded text-sm">
						<div class="flex items-center justify-between mb-1">
							<span class="font-medium text-gray-900 dark:text-white">
								{test.method} {test.url}
							</span>
							<span class="px-2 py-1 rounded text-xs {
								test.status === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
								test.status === 'error' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
								'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
							}">
								{test.status}
							</span>
						</div>
						
						{#if test.duration}
							<div class="text-gray-600 dark:text-gray-400">
								响应时间: {test.duration}ms
								{#if test.usedTauri}
									<span class="ml-2 text-blue-600 dark:text-blue-400">(通过 Tauri)</span>
								{:else}
									<span class="ml-2 text-green-600 dark:text-green-400">(通过 Fetch)</span>
								{/if}
							</div>
						{/if}
						
						{#if test.error}
							<div class="text-red-600 dark:text-red-400 mt-1">
								错误: {test.error}
							</div>
						{/if}
					</div>
				{/each}
			</div>
		</div>
	{/if}

	<!-- 最近事件 -->
	{#if recentEvents.length > 0}
		<div>
			<h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最近事件</h4>
			<div class="space-y-1 max-h-32 overflow-y-auto">
				{#each recentEvents as event}
					<div class="p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs">
						<span class="font-medium text-gray-900 dark:text-white">{event.type}</span>
						{#if event.data}
							<span class="ml-2 text-gray-600 dark:text-gray-400">
								{JSON.stringify(event.data)}
							</span>
						{/if}
					</div>
				{/each}
			</div>
		</div>
	{/if}
</div>

<style>
	.network-interceptor-test {
		max-width: 800px;
	}
</style>
