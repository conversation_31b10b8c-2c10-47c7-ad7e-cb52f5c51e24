# 网络拦截器 (Network Interceptor)

## 概述

网络拦截器是一个全局的网络请求拦截系统，专为 Tauri 2 应用设计。它可以拦截应用中的所有 `fetch` 请求，并将其转发到 Tauri 插件进行处理，同时在非 Tauri 环境中自动回退到原生 `fetch` API。

## 功能特性

- ✅ **全局拦截**: 拦截应用中的所有 `fetch` 请求
- ✅ **Tauri 2 集成**: 与 Tauri 2 插件系统无缝集成
- ✅ **自动回退**: 在非 Tauri 环境中自动使用原生 `fetch`
- ✅ **模式匹配**: 支持灵活的 URL 模式匹配和排除规则
- ✅ **实时统计**: 提供详细的网络请求统计信息
- ✅ **事件监听**: 支持网络请求事件的监听和处理
- ✅ **配置管理**: 支持动态配置更新和持久化存储
- ✅ **错误处理**: 完善的错误处理和超时机制
- ✅ **调试支持**: 内置调试模式和日志记录

## 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   应用代码      │    │   网络拦截器     │    │   Tauri 插件    │
│                 │    │                  │    │                 │
│  fetch(url)     │───▶│  拦截请求        │───▶│  处理请求       │
│                 │    │  转换格式        │    │  返回响应       │
│                 │◀───│  返回响应        │◀───│                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   原生 Fetch     │
                       │   (回退机制)     │
                       └──────────────────┘
```

## 快速开始

### 1. 初始化拦截器

拦截器会在应用启动时自动初始化，你也可以手动初始化：

```typescript
import { interceptorManager } from '$lib/network';

// 使用默认配置初始化
await interceptorManager.initialize();

// 使用自定义配置初始化
await interceptorManager.initialize({
  enabled: true,
  tauriPluginName: 'network',
  fallbackToFetch: true,
  timeout: 30000,
  interceptPatterns: ['*'],
  excludePatterns: ['data:*', 'blob:*'],
  debug: true
});
```

### 2. 使用拦截器

初始化后，所有的 `fetch` 请求都会自动被拦截：

```typescript
// 这个请求会被拦截器处理
const response = await fetch('https://api.example.com/data');
const data = await response.json();
```

### 3. 配置管理

```typescript
import { networkInterceptorActions } from '$lib/stores/network';

// 启用拦截器
await networkInterceptorActions.enable();

// 禁用拦截器
await networkInterceptorActions.disable();

// 更新配置
await networkInterceptorActions.updateConfig({
  timeout: 60000,
  interceptPatterns: ['https://api.example.com/*']
});
```

## 配置选项

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `enabled` | boolean | true | 是否启用拦截器 |
| `tauriPluginName` | string | 'network' | Tauri 插件名称 |
| `fallbackToFetch` | boolean | true | Tauri 失败时是否回退到原生 fetch |
| `timeout` | number | 30000 | 请求超时时间（毫秒） |
| `interceptPatterns` | string[] | ['*'] | 拦截模式列表 |
| `excludePatterns` | string[] | [] | 排除模式列表 |
| `debug` | boolean | false | 是否启用调试模式 |

## 模式匹配

拦截器支持灵活的 URL 模式匹配：

```typescript
// 拦截所有请求
interceptPatterns: ['*']

// 拦截特定域名
interceptPatterns: ['https://api.example.com/*']

// 拦截多个模式
interceptPatterns: [
  'https://api.example.com/*',
  'https://cdn.example.com/*'
]

// 排除特定请求
excludePatterns: [
  'data:*',           // 排除 data URLs
  'blob:*',           // 排除 blob URLs
  'ws:*',             // 排除 WebSocket
  'wss:*'             // 排除安全 WebSocket
]
```

## 事件监听

拦截器提供了丰富的事件系统：

```typescript
import { networkInterceptor } from '$lib/network';

networkInterceptor.addEventListener((event) => {
  switch (event.type) {
    case 'request:start':
      console.log('请求开始:', event.data);
      break;
    case 'request:complete':
      console.log('请求完成:', event.data);
      break;
    case 'request:error':
      console.log('请求错误:', event.data);
      break;
    case 'tauri:detected':
      console.log('检测到 Tauri 环境');
      break;
  }
});
```

## 统计信息

获取网络请求统计信息：

```typescript
import { networkStats } from '$lib/stores/network';

// 订阅统计信息
networkStats.subscribe(stats => {
  console.log('总请求数:', stats.totalRequests);
  console.log('Tauri 请求数:', stats.tauriRequests);
  console.log('Fetch 请求数:', stats.fetchRequests);
  console.log('错误数:', stats.errors);
  console.log('平均响应时间:', stats.averageResponseTime);
});
```

## Svelte 组件

项目提供了现成的 Svelte 组件：

### 设置组件

```svelte
<script>
  import NetworkInterceptorSettings from '$lib/components/network/NetworkInterceptorSettings.svelte';
</script>

<NetworkInterceptorSettings />
```

### 测试组件

```svelte
<script>
  import NetworkInterceptorTest from '$lib/components/network/NetworkInterceptorTest.svelte';
</script>

<NetworkInterceptorTest />
```

## 测试

运行自动化测试：

```typescript
import { networkTester } from '$lib/network/test';

// 运行所有测试
const results = await networkTester.runAllTests();

// 查看测试结果
console.log('测试结果:', results);

// 获取统计信息
const stats = networkTester.getStats();
console.log('统计信息:', stats);
```

## Tauri 插件要求

为了使拦截器正常工作，你需要在 Tauri 应用中实现相应的网络插件。插件应该提供以下命令：

```rust
// Tauri 插件示例 (Rust)
#[tauri::command]
async fn fetch(request: TauriPluginRequest) -> Result<TauriPluginResponse, String> {
    // 实现网络请求处理逻辑
    // ...
}
```

## 故障排除

### 常见问题

1. **拦截器未生效**
   - 检查是否正确初始化
   - 确认 `enabled` 配置为 `true`
   - 检查 URL 是否匹配拦截模式

2. **Tauri 环境检测失败**
   - 确认运行在 Tauri 应用中
   - 检查 Tauri 版本兼容性
   - 验证插件是否正确安装

3. **请求超时**
   - 调整 `timeout` 配置
   - 检查网络连接
   - 启用 `fallbackToFetch` 选项

### 调试模式

启用调试模式获取详细日志：

```typescript
await interceptorManager.initialize({
  debug: true
});
```

## 性能考虑

- 拦截器对性能的影响很小，主要开销在于请求转换
- 在非 Tauri 环境中，开销几乎为零
- 建议在生产环境中禁用调试模式
- 合理设置超时时间以避免长时间等待

## 安全注意事项

- 拦截器可以访问所有网络请求，请确保 Tauri 插件的安全性
- 避免在拦截器中记录敏感信息
- 使用 HTTPS 确保数据传输安全
- 定期更新 Tauri 和相关依赖

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的网络拦截功能
- 提供 Svelte 组件和测试工具
- 完整的文档和示例
