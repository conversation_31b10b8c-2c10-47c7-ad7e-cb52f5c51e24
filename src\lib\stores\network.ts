/**
 * 网络拦截器相关的 Svelte stores
 */

import { writable, derived, get } from 'svelte/store';
import type { NetworkInterceptorConfig, NetworkStats, InterceptorEvent } from '$lib/types/tauri';
import { interceptorManager } from '$lib/network';

// 网络拦截器配置 store
export const networkConfig = writable<NetworkInterceptorConfig>({
	enabled: true,
	tauriPluginName: 'network',
	fallbackToFetch: true,
	timeout: 30000,
	interceptPatterns: ['*'],
	excludePatterns: [
		'data:*',
		'blob:*',
		'chrome-extension:*',
		'moz-extension:*',
		'safari-extension:*',
		'ws:*',
		'wss:*'
	],
	debug: false
});

// 网络拦截器状态 store
export const networkInterceptorEnabled = writable<boolean>(true);
export const isTauriEnvironment = writable<boolean>(false);
export const networkStats = writable<NetworkStats>({
	totalRequests: 0,
	tauriRequests: 0,
	fetchRequests: 0,
	errors: 0,
	averageResponseTime: 0
});

// 网络拦截器事件 store
export const networkEvents = writable<InterceptorEvent[]>([]);

// 派生 store：拦截器是否可用
export const isNetworkInterceptorAvailable = derived(
	[networkInterceptorEnabled, isTauriEnvironment],
	([$enabled, $isTauri]) => $enabled && $isTauri
);

// 派生 store：网络请求成功率
export const networkSuccessRate = derived(
	networkStats,
	($stats) => {
		if ($stats.totalRequests === 0) return 100;
		return ((($stats.totalRequests - $stats.errors) / $stats.totalRequests) * 100).toFixed(2);
	}
);

// 网络拦截器管理函数
export const networkInterceptorActions = {
	/**
	 * 启用网络拦截器
	 */
	async enable(): Promise<void> {
		try {
			const interceptor = interceptorManager.getInterceptor();
			interceptor.enable();
			networkInterceptorEnabled.set(true);
			
			// 保存到本地存储
			localStorage.setItem('networkInterceptorEnabled', 'true');
		} catch (error) {
			console.error('启用网络拦截器失败:', error);
			throw error;
		}
	},

	/**
	 * 禁用网络拦截器
	 */
	async disable(): Promise<void> {
		try {
			const interceptor = interceptorManager.getInterceptor();
			interceptor.disable();
			networkInterceptorEnabled.set(false);
			
			// 保存到本地存储
			localStorage.setItem('networkInterceptorEnabled', 'false');
		} catch (error) {
			console.error('禁用网络拦截器失败:', error);
			throw error;
		}
	},

	/**
	 * 更新网络拦截器配置
	 */
	async updateConfig(config: Partial<NetworkInterceptorConfig>): Promise<void> {
		try {
			const interceptor = interceptorManager.getInterceptor();
			interceptor.updateConfig(config);
			
			// 更新 store
			networkConfig.update(current => ({ ...current, ...config }));
			
			// 保存到本地存储
			localStorage.setItem('networkInterceptorConfig', JSON.stringify(get(networkConfig)));
		} catch (error) {
			console.error('更新网络拦截器配置失败:', error);
			throw error;
		}
	},

	/**
	 * 重置网络统计
	 */
	resetStats(): void {
		networkStats.set({
			totalRequests: 0,
			tauriRequests: 0,
			fetchRequests: 0,
			errors: 0,
			averageResponseTime: 0
		});
	},

	/**
	 * 清除网络事件历史
	 */
	clearEvents(): void {
		networkEvents.set([]);
	},

	/**
	 * 从本地存储加载配置
	 */
	loadConfigFromStorage(): void {
		try {
			// 加载启用状态
			const enabled = localStorage.getItem('networkInterceptorEnabled');
			if (enabled !== null) {
				networkInterceptorEnabled.set(enabled === 'true');
			}

			// 加载配置
			const configStr = localStorage.getItem('networkInterceptorConfig');
			if (configStr) {
				const config = JSON.parse(configStr);
				networkConfig.set(config);
			}
		} catch (error) {
			console.error('从本地存储加载网络拦截器配置失败:', error);
		}
	},

	/**
	 * 获取当前统计信息
	 */
	refreshStats(): void {
		try {
			const interceptor = interceptorManager.getInterceptor();
			const stats = interceptor.getStats();
			networkStats.set(stats);
		} catch (error) {
			console.error('获取网络统计信息失败:', error);
		}
	}
};

// 初始化网络拦截器事件监听
export function initializeNetworkStores(): void {
	try {
		const interceptor = interceptorManager.getInterceptor();
		
		// 监听拦截器事件
		const eventListener = (event: InterceptorEvent) => {
			// 更新事件历史
			networkEvents.update(events => {
				const newEvents = [...events, event];
				// 只保留最近的 100 个事件
				return newEvents.slice(-100);
			});

			// 根据事件类型更新相应的 store
			switch (event.type) {
				case 'tauri:detected':
					isTauriEnvironment.set(true);
					break;
				case 'tauri:not_available':
					isTauriEnvironment.set(false);
					break;
				case 'interceptor:enabled':
					networkInterceptorEnabled.set(true);
					break;
				case 'interceptor:disabled':
					networkInterceptorEnabled.set(false);
					break;
				case 'request:complete':
				case 'request:error':
					// 刷新统计信息
					networkInterceptorActions.refreshStats();
					break;
			}
		};

		interceptor.addEventListener(eventListener);

		// 加载本地存储的配置
		networkInterceptorActions.loadConfigFromStorage();

		// 初始化统计信息
		networkInterceptorActions.refreshStats();

		// 检查 Tauri 环境
		isTauriEnvironment.set(interceptor.isTauriAvailable());
	} catch (error) {
		console.error('初始化网络拦截器 stores 失败:', error);
	}
}

// 导出便捷的订阅函数
export function subscribeToNetworkEvents(callback: (event: InterceptorEvent) => void): () => void {
	return networkEvents.subscribe(events => {
		if (events.length > 0) {
			callback(events[events.length - 1]);
		}
	});
}
