/**
 * 网络拦截器测试工具
 */

import { networkInterceptor } from './interceptor';
import type { NetworkInterceptorConfig, InterceptorEvent } from '$lib/types/tauri';

export interface TestResult {
	name: string;
	success: boolean;
	message: string;
	duration?: number;
	details?: any;
}

export class NetworkInterceptorTester {
	private results: TestResult[] = [];
	private events: InterceptorEvent[] = [];

	constructor() {
		// 监听拦截器事件
		networkInterceptor.addEventListener((event) => {
			this.events.push(event);
		});
	}

	/**
	 * 运行所有测试
	 */
	async runAllTests(): Promise<TestResult[]> {
		this.results = [];
		this.events = [];

		console.log('开始运行网络拦截器测试...');

		// 基础功能测试
		await this.testInitialization();
		await this.testEnvironmentDetection();
		await this.testConfigurationUpdate();
		
		// 网络请求测试
		await this.testBasicFetch();
		await this.testPostRequest();
		await this.testErrorHandling();
		await this.testPatternMatching();
		
		// 性能测试
		await this.testConcurrentRequests();

		console.log('测试完成，结果:', this.results);
		return this.results;
	}

	/**
	 * 测试初始化
	 */
	private async testInitialization(): Promise<void> {
		const testName = '拦截器初始化测试';
		const startTime = Date.now();

		try {
			await networkInterceptor.initialize({
				debug: true,
				enabled: true,
				fallbackToFetch: true
			});

			const duration = Date.now() - startTime;
			this.addResult({
				name: testName,
				success: true,
				message: '拦截器初始化成功',
				duration
			});
		} catch (error) {
			this.addResult({
				name: testName,
				success: false,
				message: `初始化失败: ${error instanceof Error ? error.message : String(error)}`,
				duration: Date.now() - startTime
			});
		}
	}

	/**
	 * 测试环境检测
	 */
	private async testEnvironmentDetection(): Promise<void> {
		const testName = '环境检测测试';

		try {
			const isTauriAvailable = networkInterceptor.isTauriAvailable();
			const isEnabled = networkInterceptor.isEnabled();

			this.addResult({
				name: testName,
				success: true,
				message: `Tauri 环境: ${isTauriAvailable ? '已检测' : '未检测'}, 拦截器: ${isEnabled ? '已启用' : '已禁用'}`,
				details: {
					isTauriAvailable,
					isEnabled
				}
			});
		} catch (error) {
			this.addResult({
				name: testName,
				success: false,
				message: `环境检测失败: ${error instanceof Error ? error.message : String(error)}`
			});
		}
	}

	/**
	 * 测试配置更新
	 */
	private async testConfigurationUpdate(): Promise<void> {
		const testName = '配置更新测试';

		try {
			const newConfig: Partial<NetworkInterceptorConfig> = {
				timeout: 15000,
				debug: false,
				interceptPatterns: ['https://api.example.com/*']
			};

			networkInterceptor.updateConfig(newConfig);

			this.addResult({
				name: testName,
				success: true,
				message: '配置更新成功',
				details: newConfig
			});
		} catch (error) {
			this.addResult({
				name: testName,
				success: false,
				message: `配置更新失败: ${error instanceof Error ? error.message : String(error)}`
			});
		}
	}

	/**
	 * 测试基本 fetch 请求
	 */
	private async testBasicFetch(): Promise<void> {
		const testName = '基本 Fetch 测试';
		const startTime = Date.now();

		try {
			const response = await fetch('https://jsonplaceholder.typicode.com/posts/1');
			const data = await response.json();
			const duration = Date.now() - startTime;

			if (response.ok && data.id === 1) {
				this.addResult({
					name: testName,
					success: true,
					message: `请求成功，状态码: ${response.status}`,
					duration,
					details: { status: response.status, dataId: data.id }
				});
			} else {
				this.addResult({
					name: testName,
					success: false,
					message: `请求失败或数据不正确，状态码: ${response.status}`,
					duration
				});
			}
		} catch (error) {
			this.addResult({
				name: testName,
				success: false,
				message: `请求异常: ${error instanceof Error ? error.message : String(error)}`,
				duration: Date.now() - startTime
			});
		}
	}

	/**
	 * 测试 POST 请求
	 */
	private async testPostRequest(): Promise<void> {
		const testName = 'POST 请求测试';
		const startTime = Date.now();

		try {
			const response = await fetch('https://jsonplaceholder.typicode.com/posts', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					title: 'Test Post',
					body: 'This is a test post',
					userId: 1
				})
			});

			const data = await response.json();
			const duration = Date.now() - startTime;

			if (response.ok && data.title === 'Test Post') {
				this.addResult({
					name: testName,
					success: true,
					message: `POST 请求成功，状态码: ${response.status}`,
					duration,
					details: { status: response.status, title: data.title }
				});
			} else {
				this.addResult({
					name: testName,
					success: false,
					message: `POST 请求失败或数据不正确，状态码: ${response.status}`,
					duration
				});
			}
		} catch (error) {
			this.addResult({
				name: testName,
				success: false,
				message: `POST 请求异常: ${error instanceof Error ? error.message : String(error)}`,
				duration: Date.now() - startTime
			});
		}
	}

	/**
	 * 测试错误处理
	 */
	private async testErrorHandling(): Promise<void> {
		const testName = '错误处理测试';
		const startTime = Date.now();

		try {
			// 尝试请求一个不存在的 URL
			await fetch('https://nonexistent-domain-for-testing.com/api/test');
			
			this.addResult({
				name: testName,
				success: false,
				message: '应该抛出错误但没有抛出',
				duration: Date.now() - startTime
			});
		} catch (error) {
			this.addResult({
				name: testName,
				success: true,
				message: '错误处理正常，成功捕获网络错误',
				duration: Date.now() - startTime,
				details: { error: error instanceof Error ? error.message : String(error) }
			});
		}
	}

	/**
	 * 测试模式匹配
	 */
	private async testPatternMatching(): Promise<void> {
		const testName = '模式匹配测试';

		try {
			// 更新配置以测试模式匹配
			networkInterceptor.updateConfig({
				interceptPatterns: ['https://jsonplaceholder.typicode.com/*'],
				excludePatterns: ['https://jsonplaceholder.typicode.com/users/*']
			});

			this.addResult({
				name: testName,
				success: true,
				message: '模式匹配配置更新成功',
				details: {
					interceptPatterns: ['https://jsonplaceholder.typicode.com/*'],
					excludePatterns: ['https://jsonplaceholder.typicode.com/users/*']
				}
			});
		} catch (error) {
			this.addResult({
				name: testName,
				success: false,
				message: `模式匹配测试失败: ${error instanceof Error ? error.message : String(error)}`
			});
		}
	}

	/**
	 * 测试并发请求
	 */
	private async testConcurrentRequests(): Promise<void> {
		const testName = '并发请求测试';
		const startTime = Date.now();

		try {
			const urls = [
				'https://jsonplaceholder.typicode.com/posts/1',
				'https://jsonplaceholder.typicode.com/posts/2',
				'https://jsonplaceholder.typicode.com/posts/3'
			];

			const promises = urls.map(url => fetch(url));
			const responses = await Promise.all(promises);
			const duration = Date.now() - startTime;

			const allSuccessful = responses.every(response => response.ok);

			if (allSuccessful) {
				this.addResult({
					name: testName,
					success: true,
					message: `并发请求成功，${responses.length} 个请求全部完成`,
					duration,
					details: { requestCount: responses.length, statuses: responses.map(r => r.status) }
				});
			} else {
				this.addResult({
					name: testName,
					success: false,
					message: '部分并发请求失败',
					duration,
					details: { statuses: responses.map(r => r.status) }
				});
			}
		} catch (error) {
			this.addResult({
				name: testName,
				success: false,
				message: `并发请求测试异常: ${error instanceof Error ? error.message : String(error)}`,
				duration: Date.now() - startTime
			});
		}
	}

	/**
	 * 添加测试结果
	 */
	private addResult(result: TestResult): void {
		this.results.push(result);
		console.log(`[测试] ${result.name}: ${result.success ? '✅' : '❌'} ${result.message}`);
	}

	/**
	 * 获取测试结果
	 */
	getResults(): TestResult[] {
		return [...this.results];
	}

	/**
	 * 获取事件历史
	 */
	getEvents(): InterceptorEvent[] {
		return [...this.events];
	}

	/**
	 * 获取统计信息
	 */
	getStats() {
		return networkInterceptor.getStats();
	}

	/**
	 * 清除测试结果
	 */
	clearResults(): void {
		this.results = [];
		this.events = [];
	}
}

// 导出测试器实例
export const networkTester = new NetworkInterceptorTester();
