<script lang="ts">
	import { onMount } from 'svelte';
	import NetworkInterceptorSettings from '$lib/components/network/NetworkInterceptorSettings.svelte';
	import NetworkInterceptorTest from '$lib/components/network/NetworkInterceptorTest.svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';

	let activeTab = 'settings';

	onMount(() => {
		// 检查 URL 参数来确定默认标签页
		const tab = $page.url.searchParams.get('tab');
		if (tab && ['settings', 'test'].includes(tab)) {
			activeTab = tab;
		}
	});

	function switchTab(tab: string) {
		activeTab = tab;
		// 更新 URL 参数
		const url = new URL($page.url);
		url.searchParams.set('tab', tab);
		goto(url.toString(), { replaceState: true });
	}
</script>

<svelte:head>
	<title>网络拦截器 - Open WebUI</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
	<div class="container mx-auto px-4 py-8">
		<!-- 页面标题 -->
		<div class="mb-8">
			<h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
				网络拦截器管理
			</h1>
			<p class="text-gray-600 dark:text-gray-400">
				配置和测试 Tauri 2 网络拦截器功能，实现全局网络请求拦截和转发。
			</p>
		</div>

		<!-- 标签页导航 -->
		<div class="mb-6">
			<nav class="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
				<button
					on:click={() => switchTab('settings')}
					class="flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors {
						activeTab === 'settings'
							? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow'
							: 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
					}"
				>
					<svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
					</svg>
					设置
				</button>
				<button
					on:click={() => switchTab('test')}
					class="flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors {
						activeTab === 'test'
							? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow'
							: 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
					}"
				>
					<svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
					</svg>
					测试
				</button>
			</nav>
		</div>

		<!-- 标签页内容 -->
		<div class="tab-content">
			{#if activeTab === 'settings'}
				<div class="animate-fade-in">
					<NetworkInterceptorSettings />
				</div>
			{:else if activeTab === 'test'}
				<div class="animate-fade-in">
					<NetworkInterceptorTest />
				</div>
			{/if}
		</div>

		<!-- 帮助信息 -->
		<div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
			<h3 class="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
				关于网络拦截器
			</h3>
			<div class="text-blue-800 dark:text-blue-200 space-y-2 text-sm">
				<p>
					<strong>功能说明:</strong> 网络拦截器可以拦截应用中的所有 fetch 请求，并将其转发到 Tauri 2 插件进行处理。
					这样可以实现更好的网络请求控制、缓存、代理等功能。
				</p>
				<p>
					<strong>使用场景:</strong>
				</p>
				<ul class="list-disc list-inside ml-4 space-y-1">
					<li>在 Tauri 应用中实现网络请求的统一管理</li>
					<li>添加请求/响应的全局拦截和修改</li>
					<li>实现网络请求的缓存和离线支持</li>
					<li>添加网络请求的监控和统计</li>
					<li>实现自定义的网络代理和路由</li>
				</ul>
				<p>
					<strong>注意事项:</strong> 在非 Tauri 环境中，拦截器会自动回退到原生的 fetch API，确保应用的正常运行。
				</p>
			</div>
		</div>
	</div>
</div>

<style>
	.animate-fade-in {
		animation: fadeIn 0.3s ease-in-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.container {
		max-width: 1200px;
	}
</style>
